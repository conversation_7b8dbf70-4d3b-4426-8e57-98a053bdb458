<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cet.electric</groupId>
        <artifactId>matterhorn-basic-service-parent</artifactId>
        <version>0.0.13</version>
        <relativePath/>
    </parent>
    <groupId>com.cet.eem</groupId>
    <artifactId>virtual-power-plant-resource-manager</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <modules>
        <module>matrix-vpp-resource-manager-common</module>
        <module>matrix-vpp-resource-manager-core</module>
        <module>matrix-vpp-resource-manager-web</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <revision>1.0.0.1</revision>
    </properties>
    <dependencyManagement>
        <dependencies>
<!--            <dependency>-->
<!--            <groupId>com.cet.electric</groupId>-->
<!--            <artifactId>fusion-matrix-v2-parent</artifactId>-->
<!--            <version>1.0.0</version>-->
<!--            <type>pom</type>-->
<!--            <scope>import</scope>-->
<!--        </dependency>-->
            <dependency>
                <groupId>com.cet.eem</groupId>
                <artifactId>eem-base-fusion-config-sdk</artifactId>
                <version>5.0.13.Alpha</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>com.cet.electric</groupId>-->
<!--                <artifactId>matterhorn-basic-service-parent</artifactId>-->
<!--                <version>0.0.13</version>-->
<!--                <type>pom</type>-->
<!--                <scope>import</scope>-->
<!--            </dependency>-->

<!--&lt;!&ndash;            <dependency>&ndash;&gt;-->
<!--&lt;!&ndash;                <groupId>com.cet.electric</groupId>&ndash;&gt;-->
<!--&lt;!&ndash;                <artifactId>cet-basic-service-dependencies</artifactId>&ndash;&gt;-->
<!--&lt;!&ndash;                <version>4.10.1</version>&ndash;&gt;-->
<!--&lt;!&ndash;                <type>pom</type>&ndash;&gt;-->
<!--&lt;!&ndash;                <scope>import</scope>&ndash;&gt;-->
<!--&lt;!&ndash;            </dependency>&ndash;&gt;-->

            <dependency>
                <groupId>com.cet.electric</groupId>
                <artifactId>fusion-matrix-v2-client</artifactId>
                <version>1.0.4</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.3.0</version>
                <inherited>true</inherited>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <!-- 避免IDE将 .flattened-pom.xml 自动识别为功能模块 -->
                            <updatePomFile>true</updatePomFile>
                            <flattenMode>resolveCiFriendliesOnly</flattenMode>
                            <pomElements>
                                <parent>expand</parent>
                                <distributionManagement>remove</distributionManagement>
                                <repositories>remove</repositories>
                            </pomElements>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                    <fork>false</fork>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-pmd-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <distributionManagement>
        <repository>
            <id>*************_9086  </id>
            <url>http://*************:9086/repository/maven-public/</url>
        </repository>
        <snapshotRepository>
            <id>*************_9088</id>
            <url>http://*************:9088/repository/cet/</url>
        </snapshotRepository>
    </distributionManagement>
</project>