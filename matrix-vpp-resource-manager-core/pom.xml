<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cet.eem</groupId>
        <artifactId>virtual-power-plant-resource-manager</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>matrix-vpp-resource-manager-core</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.cet.eem</groupId>
            <artifactId>eem-base-fusion-config-sdk</artifactId>
            <version>5.0.13.Alpha</version>
        </dependency>
        <dependency>
            <groupId>com.cet.electric</groupId>
            <artifactId>fusion-matrix-v2-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cet.eem</groupId>
            <artifactId>matrix-vpp-resource-manager-common</artifactId>
            <version>${revision}</version>
            <scope>compile</scope>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-data-redis</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
    </dependencies>


</project>