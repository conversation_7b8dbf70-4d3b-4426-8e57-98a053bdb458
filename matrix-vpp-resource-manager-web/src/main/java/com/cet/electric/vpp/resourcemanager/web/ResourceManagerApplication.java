package com.cet.electric.vpp.resourcemanager.web;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/10/17 09:50
 * @description:
 */
@SpringBootApplication
@EnableFeignClients(basePackages = {"com.cet.eem.fusion.common.feign.feign"})
public class ResourceManagerApplication {
    public static void main(String[] args) {
        SpringApplication.run(ResourceManagerApplication.class, args);
    }
}
